# 用户资料弹窗功能说明

## 功能概述

在群成员面板中，点击成员操作菜单中的"查看资料"选项，会弹出一个小窗口显示该用户的详细信息。

## 实现位置

- **组件文件**: `web/src/view/chatManager/components/GroupUserPanel.vue`
- **API接口**: `/api/imuser/findImUser?id=用户ID`

## 功能特性

### 1. 触发方式
- 在群成员列表中，每个成员右侧有一个"更多操作"按钮（三个点图标）
- 点击后显示下拉菜单，包含"私聊"和"查看资料"选项
- 点击"查看资料"即可触发用户资料弹窗

### 2. 弹窗内容
弹窗会显示以下用户信息：
- **基本信息**：
  - 用户头像（80px大小）
  - 用户昵称
  - 手机号码
  - 在线状态标签

- **详细信息**：
  - 用户ID
  - 昵称
  - 手机号码
  - 头像（可预览）
  - 加入的群组
  - 在线状态
  - 是否管理员
  - 最后登录时间
  - 创建时间
  - 更多详情

### 3. 加载状态
- 弹窗打开时会显示加载动画
- 如果API调用失败，会显示错误提示并关闭弹窗

## 代码实现

### 1. 模板部分
```vue
<el-dropdown-item :command="`profile_${member.id}`">
  <el-icon><User /></el-icon>
  查看资料
</el-dropdown-item>
```

### 2. 命令处理
```javascript
const handleCommand = (command) => {
  const [action, userId] = command.split('_')
  const member = members.value.find(m => m.id === userId)
  
  if (!member) return
  
  switch (action) {
    case 'profile':
      showMemberProfile(member)
      break
  }
}
```

### 3. 资料获取
```javascript
const showMemberProfile = async (member) => {
  try {
    profileLoading.value = true
    profileDialogVisible.value = true
    
    // 调用API获取用户详细信息
    const response = await findImUser({ id: member.id })
    
    if (response.code === 0) {
      currentUserProfile.value = response.data
    } else {
      ElMessage.error('获取用户资料失败')
      profileDialogVisible.value = false
    }
  } catch (error) {
    console.error('获取用户资料出错:', error)
    ElMessage.error('获取用户资料失败')
    profileDialogVisible.value = false
  } finally {
    profileLoading.value = false
  }
}
```

## 测试方法

1. 启动开发服务器：
   ```bash
   cd web
   npm run serve
   ```

2. 访问测试页面：
   - 路由：`/chatManager/GroupMemberTest`
   - 页面会自动加载群组数据

3. 测试步骤：
   - 点击"加载测试数据"按钮
   - 在右侧群成员面板中找到任意成员
   - 点击成员右侧的"更多操作"按钮（三个点）
   - 选择"查看资料"
   - 观察弹窗是否正常显示用户信息

## API 接口说明

### 请求
- **URL**: `http://localhost:8080/api/imuser/findImUser`
- **方法**: `GET`
- **参数**: `{ id: 用户ID }`
- **示例**: `http://localhost:8080/api/imuser/findImUser?id=10000`

### 响应格式
```json
{
  "code": 0,
  "data": {
    "id": "用户ID",
    "name": "用户昵称",
    "iphoneNum": "手机号码",
    "headImg": "头像URL",
    "groups": "加入的群组",
    "online": true,
    "isAdmin": false,
    "lastLoginTime": "最后登录时间",
    "createdAt": "创建时间",
    "moreInfo": "更多详情"
  },
  "msg": "查询成功"
}
```

## 样式说明

弹窗采用了响应式设计，包含：
- 头部区域：头像 + 基本信息
- 详情区域：使用 Element Plus 的 Descriptions 组件展示详细信息
- 底部：关闭按钮

弹窗宽度为 500px，支持拖拽，点击遮罩层不会关闭。

## 注意事项

1. 确保后端 API 接口 `/api/imuser/findImUser` 正常工作
2. 用户ID必须存在，否则会显示错误提示
3. 头像URL如果无效，会显示默认头像
4. 所有字段都有默认值处理，避免显示 undefined
